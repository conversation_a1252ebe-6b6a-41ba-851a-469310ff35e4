package com.projectSource.ultimateManhurt.game;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.banpick.BanPickManager;
import com.projectSource.ultimateManhurt.game.rules.ManhuntRules;
import com.projectSource.ultimateManhurt.game.rules.WinCondition;
import com.projectSource.ultimateManhurt.game.scoring.ScoreSystem;
import com.projectSource.ultimateManhurt.room.Room;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import com.projectSource.ultimateManhurt.world.GameWorld;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 游戏会话类
 * 代表一个正在进行的游戏实例
 */
public class GameSession {
    
    private final UltimateManhurt plugin;
    private final String sessionId;
    private final Room room;
    private GameWorld gameWorld;
    private final ManhuntRules rules;
    private final ScoreSystem scoreSystem;
    private final GameTimer timer;
    private final ImmunityManager immunityManager;
    private final CompassTracker compassTracker;
    private final BanPickManager banPickManager;
    private final com.projectSource.ultimateManhurt.game.guard.GuardModeManager guardModeManager;

    // 离线检测器
    private BukkitTask offlineCheckTask;
    
    // 游戏状态
    private GameState state = GameState.WAITING;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private WinCondition winCondition;
    
    // 玩家数据
    private final Map<UUID, PlayerRole> playerRoles = new ConcurrentHashMap<>();
    private final Set<UUID> alivePlayers = ConcurrentHashMap.newKeySet();
    private final Map<UUID, Integer> playerDeaths = new ConcurrentHashMap<>();
    private final Map<UUID, Integer> playerKills = new ConcurrentHashMap<>();
    private final Map<UUID, Double> playerDamageDealt = new ConcurrentHashMap<>();
    private final Map<UUID, Double> playerDamageTaken = new ConcurrentHashMap<>();
    
    // 冷却时间追踪
    private final Map<UUID, Long> enderPearlCooldowns = new ConcurrentHashMap<>();
    private final Map<UUID, Long> portalCooldowns = new ConcurrentHashMap<>();
    
    // 游戏统计
    private final Map<String, Object> gameStats = new ConcurrentHashMap<>();
    
    public GameSession(UltimateManhurt plugin, String sessionId, Room room, GameWorld gameWorld) {
        this.plugin = plugin;
        this.sessionId = sessionId;
        this.room = room;
        this.gameWorld = gameWorld;
        this.rules = new ManhuntRules(this);
        this.scoreSystem = new ScoreSystem(plugin, this);
        this.immunityManager = new ImmunityManager(plugin, this);
        this.compassTracker = new CompassTracker(plugin, this);
        this.banPickManager = new BanPickManager(plugin, this);
        this.guardModeManager = new com.projectSource.ultimateManhurt.game.guard.GuardModeManager(plugin, this);

        // 初始化计时器
        long gameDuration = room.getSettings().getGameDurationSeconds();
        this.timer = new GameTimer(plugin, sessionId, gameDuration, playerRoles.keySet());
    }

    /**
     * 用于Ban Pick阶段的构造函数，不需要GameWorld
     */
    public GameSession(UltimateManhurt plugin, String sessionId, Room room) {
        this.plugin = plugin;
        this.sessionId = sessionId;
        this.room = room;
        this.gameWorld = null; // Ban Pick阶段不需要游戏世界
        this.rules = new ManhuntRules(this);
        this.scoreSystem = new ScoreSystem(plugin, this);
        this.immunityManager = new ImmunityManager(plugin, this);
        this.compassTracker = new CompassTracker(plugin, this);
        this.banPickManager = new BanPickManager(plugin, this);
        this.guardModeManager = new com.projectSource.ultimateManhurt.game.guard.GuardModeManager(plugin, this);

        // 初始化计时器（Ban Pick阶段暂时不需要，但为了满足final要求）
        long gameDuration = room.getSettings().getGameDurationSeconds();
        this.timer = new GameTimer(plugin, sessionId, gameDuration, playerRoles.keySet());
        
        // 初始化玩家角色
        room.getPlayers().forEach(playerId -> {
            PlayerRole role = room.getPlayerRole(playerId);
            if (role != null) {
                playerRoles.put(playerId, role);
                if (role.isPlayer()) {
                    alivePlayers.add(playerId);
                }
            }
        });
        
        // 设置计时器回调
        timer.setOnTimeUp(t -> rules.handleTimeUp());
        timer.setOnTick(this::onTimerTick);
        timer.setOnWarning(this::onTimerWarning);
        
        plugin.getLogger().info("创建游戏会话: " + sessionId + " (房间: " + room.getName() + ")");
    }
    
    /**
     * 开始游戏
     */
    public boolean startGame() {
        if (state != GameState.WAITING) {
            return false;
        }

        if (!room.canStartGame()) {
            return false;
        }

        // 检查是否启用职业系统
        if (room.getSettings().isProfessionSystemEnabled()) {
            setState(GameState.PROFESSION_SELECTION);
            startProfessionSelection();
        } else if (room.getSettings().isBanPickEnabled()) {
            setState(GameState.BAN_PICK);
            // 在大厅中进行Ban Pick，不传送玩家
            banPickManager.startBanPick();
        } else {
            // 直接创建世界并开始游戏
            plugin.getGameManager().createGameWorldForSession(this)
                    .thenRun(this::startGameAfterBanPick)
                    .exceptionally(throwable -> {
                        plugin.getLogger().severe("创建游戏世界失败: " + throwable.getMessage());
                        endGame(com.projectSource.ultimateManhurt.game.rules.WinCondition.ADMIN_END);
                        return null;
                    });
        }

        return true;
    }

    /**
     * 开始职业选择阶段
     */
    private void startProfessionSelection() {
        broadcastMessage(ComponentUtil.info("=== 职业选择阶段开始 ==="));
        broadcastMessage(ComponentUtil.info("请选择你的职业来增强你的能力"));

        // 清理所有玩家的职业状态，确保新游戏的干净状态
        for (UUID playerId : playerRoles.keySet()) {
            plugin.getProfessionManager().removePlayerProfession(playerId);
        }
        plugin.getLogger().info("已清理所有玩家的职业状态，开始新的职业选择阶段");

        // 统计需要选择职业的在线玩家数量
        int onlinePlayersNeedingProfession = 0;

        // 为所有非观察者玩家打开职业选择GUI
        for (UUID playerId : playerRoles.keySet()) {
            PlayerRole role = playerRoles.get(playerId);
            if (role != PlayerRole.SPECTATOR) {
                Player player = Bukkit.getPlayer(playerId);
                if (player != null && player.isOnline()) {
                    onlinePlayersNeedingProfession++;
                    plugin.getGuiManager().openProfessionSelectionGui(player, this);
                }
            }
        }

        plugin.getLogger().info("房间 " + room.getName() + " 开始职业选择阶段，共有 " + onlinePlayersNeedingProfession + " 名在线玩家需要选择职业");

        // 如果没有在线玩家需要选择职业，直接继续游戏流程
        if (onlinePlayersNeedingProfession == 0) {
            plugin.getLogger().warning("没有在线玩家需要选择职业，跳过职业选择阶段");
            proceedAfterProfessionSelection();
            return;
        }

        // 设置职业选择超时（60秒）
        org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
            if (state == GameState.PROFESSION_SELECTION) {
                plugin.getLogger().warning("职业选择阶段超时，强制继续游戏");
                broadcastMessage(ComponentUtil.warning("职业选择时间已到，未选择职业的玩家将使用默认职业"));

                // 为未选择职业的玩家分配默认职业
                assignDefaultProfessions();

                // 继续游戏流程
                proceedAfterProfessionSelection();
            }
        }, 60 * 20L); // 60秒 = 1200 tick
    }

    /**
     * Ban Pick完成后开始游戏
     */
    public void onBanPickCompleted() {
        // Ban Pick完成后，创建游戏世界
        plugin.getGameManager().createGameWorldForSession(this)
                .thenRun(this::startGameAfterBanPick)
                .exceptionally(throwable -> {
                    plugin.getLogger().severe("创建游戏世界失败: " + throwable.getMessage());
                    // 结束游戏
                    endGame(com.projectSource.ultimateManhurt.game.rules.WinCondition.ADMIN_END);
                    return null;
                });
    }

    /**
     * Ban Pick后开始游戏流程
     */
    private void startGameAfterBanPick() {
        setState(GameState.STARTING);

        // 传送所有玩家到游戏世界
        teleportPlayersToGameWorld();

        // 开始倒计时
        startCountdown();
    }
    
    /**
     * 开始倒计时
     */
    private void startCountdown() {
        final int[] countdown = {5}; // 5秒倒计时
        
        Bukkit.getScheduler().runTaskTimer(plugin, task -> {
            if (countdown[0] > 0) {
                // 显示倒计时
                broadcastTitle(
                    ComponentUtil.parse("<gold><bold>" + countdown[0]),
                    ComponentUtil.parse("<yellow>游戏即将开始！"),
                    5, 15, 5
                );
                
                // 播放音效
                playCountdownSound();
                countdown[0]--;
            } else {
                // 倒计时结束，开始游戏
                task.cancel();
                actuallyStartGame();
            }
        }, 0L, 20L); // 每秒执行一次
    }
    
    /**
     * 实际开始游戏
     */
    private void actuallyStartGame() {
        setState(GameState.RUNNING);
        startTime = LocalDateTime.now();
        
        // 分发起始装备
        plugin.getStartKitManager().giveStartKitToAll(room);

        // 开始计时器
        timer.start();

        // 启动豁免期
        immunityManager.startImmunity();

        // 启动指南针追踪
        compassTracker.startTracking();

        // 如果是守卫模式，初始化守卫系统
        if (room.getSettings().getVictoryMode() == com.projectSource.ultimateManhurt.game.VictoryMode.GUARD_MODE) {
            guardModeManager.initialize();
        }

        // 通知职业系统游戏开始（用于萨满狗生成等）
        if (room.getSettings().isProfessionSystemEnabled()) {
            plugin.getProfessionManager().getPassiveSkillHandler().onGameStart();
        }

        // 通知游戏开始
        broadcastTitle(
            ComponentUtil.parse("<green><bold>游戏开始！"),
            ComponentUtil.parse("<yellow>祝你好运！"),
            10, 40, 10
        );

        broadcastMessage(ComponentUtil.success("游戏开始！祝你好运！"));

        // 播放开始音效
        playGameStartSound();

        // 更新房间状态
        room.setGameState(GameState.RUNNING);

        // 为所有玩家创建游戏Tablist
        createGameTablistForAllPlayers();

        // 启动离线检测器
        startOfflineChecker();

        plugin.getLogger().info("游戏会话 " + sessionId + " 已开始");
    }
    
    /**
     * 暂停游戏
     */
    public boolean pauseGame() {
        if (state != GameState.RUNNING) {
            return false;
        }
        
        setState(GameState.PAUSED);
        timer.pause();
        room.setGameState(GameState.PAUSED);
        
        broadcastMessage(ComponentUtil.warning("游戏已暂停"));
        
        plugin.getLogger().info("游戏会话 " + sessionId + " 已暂停");
        return true;
    }
    
    /**
     * 恢复游戏
     */
    public boolean resumeGame() {
        if (state != GameState.PAUSED) {
            return false;
        }
        
        setState(GameState.RUNNING);
        timer.resume();
        room.setGameState(GameState.RUNNING);
        
        broadcastMessage(ComponentUtil.success("游戏已恢复"));
        
        plugin.getLogger().info("游戏会话 " + sessionId + " 已恢复");
        return true;
    }
    
    /**
     * 结束游戏
     */
    public void endGame(WinCondition winCondition) {
        endGame(winCondition, true);
    }

    /**
     * 结束游戏（可选择是否计分）
     */
    public void endGame(WinCondition winCondition, boolean countScore) {
        if (state == GameState.FINISHED || state == GameState.ENDING) {
            return;
        }

        setState(GameState.ENDING);
        this.winCondition = winCondition;
        this.endTime = LocalDateTime.now();

        // 停止计时器
        timer.stop();

        // 停止指南针追踪
        compassTracker.stopTracking();

        // 如果是守卫模式，清理守卫系统
        if (room.getSettings().getVictoryMode() == com.projectSource.ultimateManhurt.game.VictoryMode.GUARD_MODE) {
            guardModeManager.cleanup();
        }

        // 停止离线检测器
        stopOfflineChecker();

        // 显示游戏结果
        showGameResults(countScore);

        // 延迟清理
        Bukkit.getScheduler().runTaskLater(plugin, this::cleanup, 200L); // 10秒后清理

        String scoreInfo = countScore ? "" : "（不计分）";
        plugin.getLogger().info("游戏会话 " + sessionId + " 已结束: " + winCondition.getDisplayName() + scoreInfo);
    }

    /**
     * 强制结束游戏
     */
    public void forceEndGame(boolean countScore) {
        endGame(WinCondition.ADMIN_END, countScore);
    }
    
    /**
     * 显示游戏结果
     */
    private void showGameResults() {
        showGameResults(true);
    }

    /**
     * 显示游戏结果（可选择是否计分）
     */
    private void showGameResults(boolean countScore) {
        if (winCondition == null) {
            return;
        }
        
        // 显示胜利/失败消息
        for (UUID playerId : playerRoles.keySet()) {
            Player player = Bukkit.getPlayer(playerId);
            if (player == null || !player.isOnline()) {
                continue;
            }
            
            PlayerRole playerRole = playerRoles.get(playerId);
            boolean isWinner = winCondition.getWinner() == playerRole;
            
            if (isWinner) {
                ComponentUtil.sendTitle(player, 
                    ComponentUtil.parse("<green><bold>胜利！"),
                    winCondition.getWinMessage(),
                    20, 100, 20
                );
                player.playSound(player.getLocation(), org.bukkit.Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.0f);
            } else {
                ComponentUtil.sendTitle(player,
                    ComponentUtil.parse("<red><bold>失败！"),
                    winCondition.getLoseMessage(),
                    20, 100, 20
                );
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);
            }
        }
        
        // 广播游戏统计
        broadcastGameStats();

        // 更新玩家统计数据
        updatePlayerStats(winCondition, countScore);
    }
    
    /**
     * 广播游戏统计
     */
    private void broadcastGameStats() {
        broadcastMessage(ComponentUtil.separator());
        broadcastMessage(ComponentUtil.parse("<gold><bold>游戏统计"));
        
        // 游戏时长
        long duration = timer.getElapsedSeconds();
        broadcastMessage(ComponentUtil.parse("<gray>游戏时长: <yellow>" + 
            com.projectSource.ultimateManhurt.util.TimeUtil.formatTime(duration)));
        
        // 击杀统计
        if (!playerKills.isEmpty()) {
            broadcastMessage(ComponentUtil.parse("<gray>击杀排行:"));
            playerKills.entrySet().stream()
                .sorted(Map.Entry.<UUID, Integer>comparingByValue().reversed())
                .limit(3)
                .forEach(entry -> {
                    Player player = Bukkit.getPlayer(entry.getKey());
                    String name = player != null ? player.getName() : "未知玩家";
                    broadcastMessage(ComponentUtil.parse("<gray>  " + name + ": <red>" + entry.getValue()));
                });
        }
        
        broadcastMessage(ComponentUtil.separator());
    }

    /**
     * 更新玩家统计数据
     */
    private void updatePlayerStats(WinCondition winCondition, boolean countScore) {
        long gameTime = timer.getElapsedSeconds() * 1000; // 转换为毫秒

        // 收集所有玩家数据用于ELO计算
        java.util.List<com.projectSource.ultimateManhurt.data.PlayerData> speedrunners = new java.util.ArrayList<>();
        java.util.List<com.projectSource.ultimateManhurt.data.PlayerData> hunters = new java.util.ArrayList<>();

        for (Map.Entry<UUID, PlayerRole> entry : playerRoles.entrySet()) {
            UUID playerId = entry.getKey();
            PlayerRole role = entry.getValue();

            if (role == PlayerRole.SPECTATOR) continue;

            com.projectSource.ultimateManhurt.data.PlayerData playerData = plugin.getDataManager().getPlayerData(playerId);
            if (playerData == null) continue;

            if (role == PlayerRole.SPEEDRUNNER) {
                speedrunners.add(playerData);
            } else if (role == PlayerRole.HUNTER) {
                hunters.add(playerData);
            }
        }

        // 只有在计分时才更新统计和ELO
        if (countScore) {
            // 计算ELO变化（如果是排位赛）
            // 现在所有房间类型都计算ELO，但只有公开房间计算战绩
            boolean countsStats = room.getType().countsStats();
            boolean calculateElo = true; // 所有房间都计算ELO

            for (Map.Entry<UUID, PlayerRole> entry : playerRoles.entrySet()) {
                UUID playerId = entry.getKey();
                PlayerRole role = entry.getValue();

                // 只更新玩家角色的统计，不包括观察者
                if (role == PlayerRole.SPECTATOR) continue;

                // 获取玩家数据
                com.projectSource.ultimateManhurt.data.PlayerData playerData = plugin.getDataManager().getPlayerData(playerId);
                if (playerData == null) continue;

                // 判断是否获胜
                boolean won = (winCondition.getWinner() == role);

                // 获取游戏内统计
                int kills = playerKills.getOrDefault(playerId, 0);
                int deaths = playerDeaths.getOrDefault(playerId, 0);

                // 记录游戏结果
                playerData.recordGameResult(won, role, kills, deaths, gameTime);

                // 更新ELO（所有房间都计算ELO）
                if (calculateElo) {
                    updatePlayerElo(playerData, role, won, speedrunners, hunters);
                }

                // 更新特殊记录
                if (role == PlayerRole.SPEEDRUNNER && winCondition == WinCondition.SPEEDRUNNER_KILL_DRAGON) {
                    // 更新最快击龙记录
                    if (gameTime < playerData.getFastestDragonKill()) {
                        playerData.setFastestDragonKill(gameTime);
                    }
                }

                // 更新最长存活记录
                if (gameTime > playerData.getLongestSurvival() * 1000L) {
                    playerData.setLongestSurvival((int) (gameTime / 1000));
                }

                // 更新最大连杀记录
                if (kills > playerData.getMaxKillStreak()) {
                    playerData.setMaxKillStreak(kills);
                }

                // 保存数据
                plugin.getDataManager().savePlayerData(playerId);
            }
        } else {
            // 不计分时显示提示信息
            broadcastMessage(ComponentUtil.warning("此次游戏因异常结束，不计入统计和排名"));
        }
    }

    /**
     * 更新玩家ELO分数（使用新的赌注池系统）
     */
    private void updatePlayerElo(com.projectSource.ultimateManhurt.data.PlayerData playerData, PlayerRole playerRole,
                                boolean won, java.util.List<com.projectSource.ultimateManhurt.data.PlayerData> speedrunners,
                                java.util.List<com.projectSource.ultimateManhurt.data.PlayerData> hunters) {

        // 获取阵营人数
        int speedrunnerCount = speedrunners.size();
        int hunterCount = hunters.size();

        // 确定获胜方角色
        PlayerRole winnerRole = winCondition.getWinner();

        // 使用新的ELO计算方法
        int eloChange = com.projectSource.ultimateManhurt.ranking.EloSystem.calculateEloChange(
            speedrunnerCount, hunterCount, won, playerRole, winnerRole);

        // 更新ELO
        com.projectSource.ultimateManhurt.ranking.EloSystem.updatePlayerElo(playerData, eloChange);

        // 通知玩家ELO变化
        Player player = plugin.getServer().getPlayer(playerData.getPlayerId());
        if (player != null) {
            int newElo = com.projectSource.ultimateManhurt.ranking.EloSystem.getPlayerElo(playerData);
            com.projectSource.ultimateManhurt.ranking.EloSystem.Rank newRank =
                com.projectSource.ultimateManhurt.ranking.EloSystem.getRank(newElo);

            String eloChangeStr = (eloChange >= 0 ? "+" : "") + eloChange;

            // 构建详细的ELO变化消息
            String roleInfo = playerRole == PlayerRole.SPEEDRUNNER ? "速通者" : "猎人";
            String resultInfo = won ? "获胜" : "失败";

            // 计算赌注池信息用于显示
            int totalStake = (speedrunnerCount + hunterCount) * 25;
            String stakeInfo = String.format("(赌注池: %d分, 速通者%d人, 猎人%d人)",
                totalStake, speedrunnerCount, hunterCount);

            // 使用Component构建器来避免颜色标签嵌套问题
            net.kyori.adventure.text.Component message = net.kyori.adventure.text.Component.text()
                .append(net.kyori.adventure.text.Component.text("ℹ ", net.kyori.adventure.text.format.NamedTextColor.AQUA))
                .append(net.kyori.adventure.text.Component.text(roleInfo + resultInfo + " ELO变化: " + eloChangeStr + " ", net.kyori.adventure.text.format.NamedTextColor.AQUA))
                .append(net.kyori.adventure.text.Component.text(stakeInfo, net.kyori.adventure.text.format.NamedTextColor.GRAY))
                .append(net.kyori.adventure.text.Component.text(" (当前: " + newElo + " ", net.kyori.adventure.text.format.NamedTextColor.AQUA))
                .append(com.projectSource.ultimateManhurt.util.ComponentUtil.parse(newRank.getColoredName()))
                .append(net.kyori.adventure.text.Component.text(")", net.kyori.adventure.text.format.NamedTextColor.AQUA))
                .build();

            com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(player, message);

            // 更新玩家段位显示
            plugin.getRankDisplayManager().updatePlayerRankDisplay(player);
        }
    }

    /**
     * 清理游戏会话
     */
    private void cleanup() {
        setState(GameState.FINISHED);
        room.setGameState(GameState.FINISHED);

        // 清理豁免管理器
        immunityManager.cleanup();

        // 传送玩家回主世界
        teleportPlayersToMainWorld();

        // 为所有玩家创建大厅Tablist
        createLobbyTablistForAllPlayers();

        // 清理玩家位置数据
        for (UUID playerId : playerRoles.keySet()) {
            plugin.getPlayerLocationManager().removePlayerLocation(playerId);
        }

        // 清理职业系统状态
        if (room.getSettings().isProfessionSystemEnabled()) {
            // 强制清理所有灵魂虹吸效果
            plugin.getProfessionManager().getPassiveSkillHandler().forceCleanupAllSoulSiphon();

            for (UUID playerId : playerRoles.keySet()) {
                plugin.getProfessionManager().removePlayerProfession(playerId);
                plugin.getLogger().info("已清理玩家 " + playerId + " 的职业状态");
            }

            // 清理活跃的职业选择GUI
            com.projectSource.ultimateManhurt.profession.gui.ProfessionSelectionGui.clearAllActiveProfessionGuis();
            plugin.getLogger().info("已清理所有活跃的职业选择GUI");
        }

        // 通知游戏管理器清理
        plugin.getGameManager().removeGameSession(sessionId);

        // 延迟重置房间状态，让玩家有时间查看结果
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            room.resetAfterGame();

            // 通知房间内的玩家游戏已结束，可以开始新游戏
            for (UUID playerId : room.getPlayers()) {
                Player player = Bukkit.getPlayer(playerId);
                if (player != null && player.isOnline()) {
                    ComponentUtil.sendMessage(player, ComponentUtil.info("游戏已结束，房间已重置，可以开始新游戏"));
                }
            }

            plugin.getLogger().info("房间 " + room.getName() + " 已重置，可以开始新游戏");
        }, 100L); // 5秒后重置

        plugin.getLogger().info("游戏会话 " + sessionId + " 已清理");
    }

    // 辅助方法

    /**
     * 传送玩家到游戏世界
     */
    private void teleportPlayersToGameWorld() {
        for (UUID playerId : playerRoles.keySet()) {
            Player player = Bukkit.getPlayer(playerId);
            if (player == null || !player.isOnline()) {
                continue;
            }

            // 重置玩家状态
            resetPlayerState(player);

            PlayerRole role = playerRoles.get(playerId);
            switch (role) {
                case SPEEDRUNNER:
                    player.teleport(gameWorld.getSpeedrunnerSpawn());
                    player.setGameMode(org.bukkit.GameMode.SURVIVAL);
                    break;
                case HUNTER:
                    player.teleport(gameWorld.getHunterSpawn());
                    player.setGameMode(org.bukkit.GameMode.SURVIVAL);
                    break;
                case SPECTATOR:
                    player.teleport(gameWorld.getSpectatorSpawn());
                    player.setGameMode(org.bukkit.GameMode.SPECTATOR);
                    break;
            }
        }
    }

    /**
     * 重置玩家状态（清空物品、移除buff等）
     */
    private void resetPlayerState(Player player) {
        // 清空物品栏
        player.getInventory().clear();

        // 清空盔甲栏
        player.getInventory().setArmorContents(null);

        // 清空副手
        player.getInventory().setItemInOffHand(null);

        // 移除所有药水效果
        for (org.bukkit.potion.PotionEffect effect : player.getActivePotionEffects()) {
            player.removePotionEffect(effect.getType());
        }

        // 设置血量（根据角色设置最大血量）
        PlayerRole role = getPlayerRole(player.getUniqueId());
        if (role != null) {
            double maxHealth = 20.0; // 默认血量
            if (role == PlayerRole.SPEEDRUNNER) {
                maxHealth = room.getSettings().getSpeedrunnerMaxHealth();
            } else if (role == PlayerRole.HUNTER) {
                maxHealth = room.getSettings().getHunterMaxHealth();
            }

            // 设置最大血量和当前血量
            player.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).setBaseValue(maxHealth);
            player.setHealth(maxHealth);
        } else {
            // 如果角色为null，使用默认血量
            double defaultMaxHealth = player.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
            player.setHealth(defaultMaxHealth);
        }

        player.getAttribute(org.bukkit.attribute.Attribute.ATTACK_DAMAGE).setBaseValue(1.0);

        player.getAttribute(org.bukkit.attribute.Attribute.MOVEMENT_SPEED).setBaseValue(0.1);
        player.getAttribute(org.bukkit.attribute.Attribute.ATTACK_SPEED).setBaseValue(4.0);

        // 重置饥饿值
        player.setFoodLevel(20);
        player.setSaturation(20.0f);
        player.setExhaustion(0.0f);

        // 重置经验
        player.setExp(0.0f);
        player.setLevel(0);
        player.setTotalExperience(0);

        // 清除火焰状态
        player.setFireTicks(0);

        // 重置飞行状态
        player.setAllowFlight(false);
        player.setFlying(false);

        // 重置隐身状态
        player.setInvisible(false);

        // 重置游戏模式（会在传送时再次设置）
        player.setGameMode(org.bukkit.GameMode.SURVIVAL);

        plugin.getLogger().info("已重置玩家 " + player.getName() + " 的状态");
    }

    /**
     * 传送玩家回主世界
     */
    private void teleportPlayersToMainWorld() {
        for (UUID playerId : playerRoles.keySet()) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                // 重置玩家状态
                resetPlayerState(player);

                // 传送回主世界
                plugin.getWorldManager().teleportToMainWorld(player);
                player.setGameMode(org.bukkit.GameMode.SURVIVAL);
            }
        }
    }

    /**
     * 广播消息给所有玩家
     */
    public void broadcastMessage(net.kyori.adventure.text.Component message) {
        for (UUID playerId : playerRoles.keySet()) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                ComponentUtil.sendMessage(player, message);
            }
        }
    }

    /**
     * 广播标题给所有玩家
     */
    private void broadcastTitle(net.kyori.adventure.text.Component title,
                               net.kyori.adventure.text.Component subtitle,
                               int fadeIn, int stay, int fadeOut) {
        for (UUID playerId : playerRoles.keySet()) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                ComponentUtil.sendTitle(player, title, subtitle, fadeIn, stay, fadeOut);
            }
        }
    }

    /**
     * 播放倒计时音效
     */
    private void playCountdownSound() {
        for (UUID playerId : playerRoles.keySet()) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.0f);
            }
        }
    }

    /**
     * 播放游戏开始音效
     */
    private void playGameStartSound() {
        for (UUID playerId : playerRoles.keySet()) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.0f);
            }
        }
    }

    /**
     * 为所有玩家创建游戏Tablist
     */
    private void createGameTablistForAllPlayers() {
        for (UUID playerId : playerRoles.keySet()) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                plugin.getTablistManager().createGameTablist(player, this);
            }
        }
    }

    /**
     * 为所有玩家创建大厅Tablist
     */
    private void createLobbyTablistForAllPlayers() {
        for (UUID playerId : playerRoles.keySet()) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                plugin.getTablistManager().createDefaultTablist(player);
            }
        }
    }

    /**
     * 计时器滴答回调
     */
    private void onTimerTick(GameTimer timer) {
        // 更新计分板等
        plugin.getScoreboardManager().updateGameScoreboard(this);
        // 更新Tablist
        plugin.getTablistManager().updateGameTablist(this);
    }

    /**
     * 计时器警告回调
     */
    private void onTimerWarning(GameTimer timer) {
        // 可以在这里添加特殊的警告逻辑
    }

    /**
     * 设置游戏状态
     */
    private void setState(GameState newState) {
        if (this.state != newState) {
            GameState oldState = this.state;
            this.state = newState;
            plugin.getLogger().info("游戏会话 " + sessionId + " 状态变更: " + oldState + " -> " + newState);

            // 更新Tablist以反映状态变化
            plugin.getTablistManager().updateGameTablist(this);
        }
    }

    // 公共方法

    /**
     * 记录玩家死亡
     */
    public void recordDeath(UUID playerId) {
        playerDeaths.merge(playerId, 1, Integer::sum);
        alivePlayers.remove(playerId);

        // 更新Tablist以反映存活玩家变化
        plugin.getTablistManager().updateGameTablist(this);
    }

    /**
     * 恢复玩家存活状态
     */
    public void revivePlayer(UUID playerId) {
        PlayerRole role = playerRoles.get(playerId);
        if (role != null && role.isPlayer()) {
            alivePlayers.add(playerId);

            // 更新Tablist以反映存活玩家变化
            plugin.getTablistManager().updateGameTablist(this);
        }
    }

    /**
     * 记录玩家击杀
     */
    public void recordKill(UUID killerId) {
        playerKills.merge(killerId, 1, Integer::sum);
    }

    /**
     * 记录伤害
     */
    public void recordDamage(UUID attackerId, UUID victimId, double damage) {
        playerDamageDealt.merge(attackerId, damage, Double::sum);
        playerDamageTaken.merge(victimId, damage, Double::sum);
    }

    /**
     * 检查玩家是否存活
     */
    public boolean isPlayerAlive(UUID playerId) {
        return alivePlayers.contains(playerId);
    }

    /**
     * 获取指定角色的玩家
     */
    public Set<UUID> getPlayersByRole(PlayerRole role) {
        Set<UUID> result = new HashSet<>();
        for (Map.Entry<UUID, PlayerRole> entry : playerRoles.entrySet()) {
            if (entry.getValue() == role) {
                result.add(entry.getKey());
            }
        }
        return result;
    }

    /**
     * 获取玩家角色
     */
    public PlayerRole getPlayerRole(UUID playerId) {
        return playerRoles.get(playerId);
    }

    /**
     * 通知玩家角色变化（用于指南针追踪等功能）
     */
    public void notifyPlayerRoleChanged(UUID playerId, PlayerRole newRole) {
        PlayerRole oldRole = playerRoles.get(playerId);
        if (oldRole != newRole) {
            playerRoles.put(playerId, newRole);

            // 通知指南针追踪器
            compassTracker.onPlayerRoleChanged(playerId, newRole);

            // 更新存活玩家列表
            if (newRole != null && newRole.isPlayer()) {
                alivePlayers.add(playerId);
            } else {
                alivePlayers.remove(playerId);
            }
        }
    }

    /**
     * 通知玩家加入游戏
     */
    public void notifyPlayerJoinGame(UUID playerId) {
        compassTracker.onPlayerJoinGame(playerId);
    }

    /**
     * 通知玩家离开游戏
     */
    public void notifyPlayerLeaveGame(UUID playerId) {
        compassTracker.onPlayerLeaveGame(playerId);
    }

    /**
     * 获取末影珍珠最后使用时间
     */
    public long getLastEnderPearlUse(UUID playerId) {
        return enderPearlCooldowns.getOrDefault(playerId, 0L);
    }

    /**
     * 设置末影珍珠最后使用时间
     */
    public void setLastEnderPearlUse(UUID playerId, long time) {
        enderPearlCooldowns.put(playerId, time);
    }

    /**
     * 获取传送门最后使用时间
     */
    public long getLastPortalUse(UUID playerId) {
        return portalCooldowns.getOrDefault(playerId, 0L);
    }

    /**
     * 设置传送门最后使用时间
     */
    public void setLastPortalUse(UUID playerId, long time) {
        portalCooldowns.put(playerId, time);
    }

    /**
     * 处理玩家职业选择完成
     */
    public void onPlayerProfessionSelected(UUID playerId, com.projectSource.ultimateManhurt.profession.Profession profession) {
        // 记录职业选择完成
        plugin.getLogger().info("玩家 " + playerId + " 选择了职业: " + profession.getDisplayName());

        // 检查是否所有玩家都已选择职业
        checkAllPlayersSelectedProfession();
    }

    /**
     * 检查是否所有玩家都已选择职业
     */
    private void checkAllPlayersSelectedProfession() {
        if (!room.getSettings().isProfessionSystemEnabled()) {
            return; // 职业系统未启用
        }

        // 统计需要选择职业的在线玩家
        int totalPlayersNeedingProfession = 0;
        int playersWithProfession = 0;

        for (UUID playerId : playerRoles.keySet()) {
            PlayerRole role = playerRoles.get(playerId);
            if (role != PlayerRole.SPECTATOR) {
                // 检查玩家是否在线
                Player player = Bukkit.getPlayer(playerId);
                if (player != null && player.isOnline()) {
                    totalPlayersNeedingProfession++;
                    if (plugin.getProfessionManager().hasPlayerProfession(playerId)) {
                        playersWithProfession++;
                    }
                }
            }
        }

        // 记录调试信息
        plugin.getLogger().info("职业选择进度: " + playersWithProfession + "/" + totalPlayersNeedingProfession + " 玩家已选择职业");

        // 详细记录每个玩家的职业选择状态
        for (UUID playerId : playerRoles.keySet()) {
            PlayerRole role = playerRoles.get(playerId);
            if (role != PlayerRole.SPECTATOR) {
                Player player = Bukkit.getPlayer(playerId);
                if (player != null && player.isOnline()) {
                    boolean hasProfession = plugin.getProfessionManager().hasPlayerProfession(playerId);
                    com.projectSource.ultimateManhurt.profession.Profession profession = plugin.getProfessionManager().getPlayerProfession(playerId);
                    plugin.getLogger().info("玩家 " + player.getName() + " (角色: " + role + ") - 已选择职业: " + hasProfession +
                        (profession != null ? " (职业: " + profession.getDisplayName() + ")" : ""));
                }
            }
        }

        // 只有当所有在线的非观察者玩家都选择了职业时才继续
        if (totalPlayersNeedingProfession > 0 && playersWithProfession >= totalPlayersNeedingProfession) {
            // 所有在线玩家都已选择职业，继续游戏流程
            plugin.getLogger().info("所有在线玩家都已选择职业，继续游戏流程");
            proceedAfterProfessionSelection();
        }
    }

    /**
     * 为未选择职业的玩家分配默认职业
     */
    private void assignDefaultProfessions() {
        for (UUID playerId : playerRoles.keySet()) {
            PlayerRole role = playerRoles.get(playerId);
            if (role != PlayerRole.SPECTATOR) {
                Player player = Bukkit.getPlayer(playerId);
                if (player != null && player.isOnline()) {
                    if (!plugin.getProfessionManager().hasPlayerProfession(playerId)) {
                        // 根据角色分配默认职业
                        com.projectSource.ultimateManhurt.profession.Profession defaultProfession = getDefaultProfessionForRole(role);
                        if (defaultProfession != null) {
                            boolean success = plugin.getProfessionManager().setProfession(playerId, defaultProfession);
                            if (success) {
                                ComponentUtil.sendMessage(player, ComponentUtil.warning(
                                    "由于超时，已为你分配默认职业: " + defaultProfession.getDisplayName()));
                                plugin.getLogger().info("为玩家 " + player.getName() + " 分配默认职业: " + defaultProfession.getDisplayName());
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取角色的默认职业
     */
    private com.projectSource.ultimateManhurt.profession.Profession getDefaultProfessionForRole(PlayerRole role) {
        com.projectSource.ultimateManhurt.profession.Profession[] availableProfessions =
            com.projectSource.ultimateManhurt.profession.Profession.getAvailableProfessions(role);

        // 返回第一个可用职业作为默认职业
        return availableProfessions.length > 0 ? availableProfessions[0] : null;
    }

    /**
     * 职业选择完成后继续游戏流程
     */
    private void proceedAfterProfessionSelection() {
        broadcastMessage(ComponentUtil.success("所有玩家职业选择完成！"));

        // 检查是否启用Ban Pick
        if (room.getSettings().isBanPickEnabled()) {
            setState(GameState.BAN_PICK);
            banPickManager.startBanPick();
        } else {
            // 直接创建世界并开始游戏
            plugin.getGameManager().createGameWorldForSession(this)
                    .thenRun(this::startGameAfterBanPick)
                    .exceptionally(throwable -> {
                        plugin.getLogger().severe("创建游戏世界失败: " + throwable.getMessage());
                        endGame(com.projectSource.ultimateManhurt.game.rules.WinCondition.ADMIN_END);
                        return null;
                    });
        }
    }

    // Getter方法
    public String getSessionId() { return sessionId; }
    public Room getRoom() { return room; }
    public GameWorld getGameWorld() { return gameWorld; }
    public UltimateManhurt getPlugin() { return plugin; }
    public ManhuntRules getRules() { return rules; }
    public ScoreSystem getScoreSystem() { return scoreSystem; }
    public GameTimer getTimer() { return timer; }
    public ImmunityManager getImmunityManager() { return immunityManager; }
    public CompassTracker getCompassTracker() { return compassTracker; }
    public BanPickManager getBanPickManager() { return banPickManager; }
    public com.projectSource.ultimateManhurt.game.guard.GuardModeManager getGuardModeManager() { return guardModeManager; }

    /**
     * 设置游戏世界（Ban Pick完成后调用）
     */
    public void setGameWorld(GameWorld gameWorld) {
        this.gameWorld = gameWorld;
    }
    public GameState getState() { return state; }
    public LocalDateTime getStartTime() { return startTime; }
    public LocalDateTime getEndTime() { return endTime; }
    public WinCondition getWinCondition() { return winCondition; }
    public Map<UUID, PlayerRole> getPlayerRoles() { return new HashMap<>(playerRoles); }
    public Set<UUID> getAlivePlayers() { return new HashSet<>(alivePlayers); }
    public Map<UUID, Integer> getPlayerDeaths() { return new HashMap<>(playerDeaths); }
    public int getPlayerDeaths(UUID playerId) { return playerDeaths.getOrDefault(playerId, 0); }
    public Map<UUID, Integer> getPlayerKills() { return new HashMap<>(playerKills); }
    public Map<UUID, Double> getPlayerDamageDealt() { return new HashMap<>(playerDamageDealt); }
    public Map<UUID, Double> getPlayerDamageTaken() { return new HashMap<>(playerDamageTaken); }

    /**
     * 获取游戏时间（毫秒）
     */
    public long getGameTime() {
        if (timer == null) {
            return 0L;
        }
        return timer.getElapsedSeconds() * 1000L;
    }

    /**
     * 启动离线检测器
     * 每5分钟检查一次所有玩家是否离线，如果都离线则强制结束游戏
     */
    private void startOfflineChecker() {
        // 5分钟 = 300秒 = 6000 ticks
        long checkInterval = 5 * 60 * 20L;

        offlineCheckTask = new BukkitRunnable() {
            @Override
            public void run() {
                checkAllPlayersOffline();
            }
        }.runTaskTimer(plugin, checkInterval, checkInterval);

        plugin.getLogger().info("游戏 " + sessionId + " 离线检测器已启动，每5分钟检查一次");
    }

    /**
     * 停止离线检测器
     */
    private void stopOfflineChecker() {
        if (offlineCheckTask != null) {
            offlineCheckTask.cancel();
            offlineCheckTask = null;
            plugin.getLogger().info("游戏 " + sessionId + " 离线检测器已停止");
        }
    }

    /**
     * 检查所有玩家是否都离线
     */
    private void checkAllPlayersOffline() {
        if (state != GameState.RUNNING) {
            // 游戏不在进行中，不需要检查
            return;
        }

        boolean hasOnlinePlayer = false;
        int totalPlayers = playerRoles.size();
        int onlinePlayers = 0;

        for (UUID playerId : playerRoles.keySet()) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                hasOnlinePlayer = true;
                onlinePlayers++;
            }
        }

        plugin.getLogger().info("离线检测 - 游戏: " + sessionId + ", 总玩家: " + totalPlayers + ", 在线: " + onlinePlayers);

        if (!hasOnlinePlayer) {
            // 所有玩家都离线，强制结束游戏
            plugin.getLogger().warning("游戏 " + sessionId + " 所有玩家都离线，强制结束游戏（不计分）");

            // 广播消息给可能重连的玩家
            broadcastMessage(ComponentUtil.warning("所有玩家离线超过5分钟，游戏已强制结束"));

            // 强制结束游戏，不计分
            forceEndGame(false);
        }
    }
}
