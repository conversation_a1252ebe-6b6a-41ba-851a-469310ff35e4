package com.projectSource.ultimateManhurt.profession.listener;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.profession.Profession;
import com.projectSource.ultimateManhurt.profession.skill.PassiveSkillHandler;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import com.projectSource.ultimateManhurt.profession.skill.ActiveSkillHandler;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.entity.EntityRegainHealthEvent;
import org.bukkit.event.entity.EntityShootBowEvent;
import org.bukkit.event.entity.ProjectileHitEvent;
import org.bukkit.entity.Arrow;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.event.entity.EntityPickupItemEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.player.PlayerItemConsumeEvent;
import org.bukkit.event.entity.PotionSplashEvent;
import org.bukkit.inventory.ItemStack;

/**
 * 职业系统事件监听器
 */
public class ProfessionListener implements Listener {
    
    private final UltimateManhurt plugin;
    private final PassiveSkillHandler passiveSkillHandler;
    
    public ProfessionListener(UltimateManhurt plugin) {
        this.plugin = plugin;
        this.passiveSkillHandler = plugin.getProfessionManager().getPassiveSkillHandler();
    }
    
    /**
     * 处理玩家受到伤害事件（被动技能触发）
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerDamage(EntityDamageByEntityEvent event) {
        if (!(event.getEntity() instanceof Player victim)) {
            return;
        }
        
        if (!(event.getDamager() instanceof Player attacker)) {
            return;
        }
        
        // 检查受害者是否在游戏中
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(victim.getUniqueId());
        if (gameSession == null) {
            return;
        }
        
        // 检查房间是否启用职业系统
        if (!gameSession.getRoom().getSettings().isProfessionSystemEnabled()) {
            return;
        }

        // 检查受害者是否是处于龙卷风暴无敌状态的萨满
        if (plugin.getProfessionManager().getActiveSkillHandler().isShamanInvulnerable(victim.getUniqueId())) {
            event.setCancelled(true);
            ComponentUtil.sendMessage(attacker, ComponentUtil.warning("萨满处于龙卷风暴无敌状态，无法攻击！"));
            return;
        }

        // 检查森之祝福闪避效果（对所有受到光环影响的玩家生效）
        if (passiveSkillHandler.handleForestBlessingDodge(victim)) {
            event.setCancelled(true); // 闪避成功，取消伤害
            return;
        }



        // 猪灵光环伤害加深处理
        handlePiglinAuraDamageBoost(event, attacker, victim);

        // 萨满狗战斗AI处理
        handleShamanDogCombat(attacker, victim);

        // 获取受害者职业
        Profession victimProfession = plugin.getProfessionManager().getPlayerProfession(victim.getUniqueId());
        if (victimProfession == null) {
            return;
        }
        
        // 根据职业处理被动技能
        switch (victimProfession) {
            case ENDERMAN:
                // 末影人闪烁
                passiveSkillHandler.handleEndermanBlink(victim, attacker);
                break;
                
            case BUTCHER:
                // 屠夫腐肉堆积（仅近战伤害）
                if (isMeleeAttack(event)) {
                    passiveSkillHandler.handleButcherFleshStack(victim);
                }
                break;
                
            case IRON_GOLEM:
                // 铁傀儡强击
                boolean immuneDamage = passiveSkillHandler.handleIronGolemCounterAttack(victim, attacker, event.getDamage());
                if (immuneDamage) {
                    event.setCancelled(true);
                }
                break;

            case SPIDER:
                // 蜘蛛毒液狂飙
                passiveSkillHandler.handleSpiderVenomRage(victim, attacker);
                break;

            case LENA:
                // 莱娜受攻击处理（森之祝福闪避已在通用位置处理）
                break;

            case CAPTAIN:
                // 船长朗姆酒被动技能
                double originalDamage = event.getDamage();
                double modifiedDamage = passiveSkillHandler.handleCaptainRumPassive(victim, originalDamage, event.getCause());
                if (modifiedDamage != originalDamage) {
                    event.setDamage(modifiedDamage);
                }
                break;

            case ZOMBIE:
                // 僵尸回光返照主动技能效果
                if (plugin.getProfessionManager().getActiveSkillHandler().isZombieLastStandActive(victim.getUniqueId())) {
                    // 取消伤害并转化为治疗
                    event.setCancelled(true);

                    // 治疗僵尸（伤害值等于原本要受到的伤害）
                    double healAmount = event.getDamage();
                    double currentHealth = victim.getHealth();
                    double maxHealth = victim.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
                    double newHealth = Math.min(maxHealth, currentHealth + healAmount);
                    victim.setHealth(newHealth);

                    // 播放治疗效果
                    victim.playSound(victim.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.2f);
                    victim.spawnParticle(org.bukkit.Particle.HEART, victim.getLocation().add(0, 1, 0), 5, 0.5, 0.5, 0.5, 0.1);

                    // 通知玩家
                    com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(victim,
                        com.projectSource.ultimateManhurt.util.ComponentUtil.info("回光返照：伤害转化为 " + String.format("%.1f", healAmount) + " 点治疗！"));
                    com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(attacker,
                        com.projectSource.ultimateManhurt.util.ComponentUtil.warning("攻击被回光返照效果转化为治疗！"));
                }
                break;

            default:
                break;
        }

        // 检查船长海灵诅咒伤害分担机制
        handleCaptainSeaCurseDamageSharing(victim, event);

        // 获取攻击者职业（用于其他被动技能）
        Profession attackerProfession = plugin.getProfessionManager().getPlayerProfession(attacker.getUniqueId());

        // 处理攻击者的被动技能
        if (attackerProfession == Profession.SHADOW_ASSASSIN) {
            // 暗影刺客背刺
            passiveSkillHandler.handleShadowAssassinBackstab(attacker, victim, event);

            // 如果暗影刺客处于隐身状态，攻击后现形
            if (plugin.getProfessionManager().getActiveSkillHandler().isShadowAssassinInvisible(attacker.getUniqueId())) {
                plugin.getProfessionManager().getActiveSkillHandler().revealShadowAssassin(attacker);
            }
        }

        // 僵尸尸鬼狂怒被动技能
        if (attackerProfession == Profession.ZOMBIE) {
            // 检查被攻击者是否为速通者
            com.projectSource.ultimateManhurt.game.PlayerRole victimRole = plugin.getGameManager()
                    .getGameSessionByPlayer(victim.getUniqueId()).getPlayerRole(victim.getUniqueId());
            if (victimRole == com.projectSource.ultimateManhurt.game.PlayerRole.SPEEDRUNNER) {
                passiveSkillHandler.handleZombieGhoulRage(attacker, victim, event.getFinalDamage());
            }
        }

        // 恐惧魔王现在只在击杀时触发灵魂虹吸，攻击时不再触发

        // 检查攻击者的战斗专注状态（猎人职业）
        if (attackerProfession == Profession.HUNTER) {
            ActiveSkillHandler activeHandler = plugin.getProfessionManager().getActiveSkillHandler();
            if (activeHandler.isCombatFocusActive(attacker.getUniqueId())) {
                // 近战攻击回复1.5点生命值
                if (isMeleeAttack(event)) {
                    double currentHealth = attacker.getHealth();
                    double maxHealth = attacker.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
                    attacker.setHealth(Math.min(maxHealth, currentHealth + 1.5));

                    com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(attacker,
                        com.projectSource.ultimateManhurt.util.ComponentUtil.info("战斗专注：近战回复生命值！"));
                }
            }
        }
    }
    
    /**
     * 处理玩家射箭事件（骷髅积少成多）
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerShootBow(EntityShootBowEvent event) {
        if (!(event.getEntity() instanceof Player player)) {
            return;
        }
        
        // 检查玩家是否在游戏中
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession == null) {
            return;
        }
        
        // 检查房间是否启用职业系统
        if (!gameSession.getRoom().getSettings().isProfessionSystemEnabled()) {
            return;
        }
        
        // 获取玩家职业
        Profession profession = plugin.getProfessionManager().getPlayerProfession(player.getUniqueId());
        if (profession == Profession.SKELETON) {
            // 骷髅积少成多
            passiveSkillHandler.handleSkeletonAccumulation(player);

            // 检查巫妖诅咒是否激活
            ActiveSkillHandler activeHandler = plugin.getProfessionManager().getActiveSkillHandler();
            if (activeHandler.isLichCurseActive(player.getUniqueId())) {
                // 扣除13%当前生命值
                double currentHealth = player.getHealth();
                double damage = currentHealth * 0.13;
                player.setHealth(Math.max(1.0, currentHealth - damage)); // 确保不会死亡

                // 箭矢将在命中时应用诅咒效果（需要在ProjectileHitEvent中处理）
                // 这里先标记箭矢
                if (event.getProjectile() != null) {
                    event.getProjectile().setMetadata("lich_curse",
                        new org.bukkit.metadata.FixedMetadataValue(plugin, player.getUniqueId().toString()));
                }
            }
        }
    }
    
    /**
     * 处理方块破坏事件（探险家寻宝直觉）
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onBlockBreak(BlockBreakEvent event) {
        if (event.isCancelled()) {
            return;
        }
        
        Player player = event.getPlayer();
        
        // 检查玩家是否在游戏中
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession == null) {
            return;
        }
        
        // 检查房间是否启用职业系统
        if (!gameSession.getRoom().getSettings().isProfessionSystemEnabled()) {
            return;
        }
        
        // 获取玩家职业
        Profession profession = plugin.getProfessionManager().getPlayerProfession(player.getUniqueId());
        if (profession == Profession.EXPLORER) {
            // 探险家寻宝直觉
            passiveSkillHandler.handleExplorerTreasureInstinct(player, event.getBlock().getType());
        }
    }
    
    /**
     * 处理玩家右键事件（主动技能触发）
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        
        // 检查是否是右键点击
        if (!event.getAction().name().contains("RIGHT_CLICK")) {
            return;
        }
        
        // 检查副手是否有剑
        ItemStack offhandItem = player.getInventory().getItemInOffHand();
        if (!isSword(offhandItem.getType())) {
            return;
        }
        
        // 检查玩家是否在游戏中
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(player.getUniqueId());
        if (gameSession == null) {
            return;
        }
        
        // 检查房间是否启用职业系统
        if (!gameSession.getRoom().getSettings().isProfessionSystemEnabled()) {
            return;
        }
        
        // 获取玩家职业
        Profession profession = plugin.getProfessionManager().getPlayerProfession(player.getUniqueId());
        if (profession == null) {
            return;
        }
        
        // 使用主动技能
        boolean success = plugin.getProfessionManager().useActiveSkill(player.getUniqueId());
        if (success) {
            event.setCancelled(true); // 取消右键事件，避免其他交互
        }
    }
    
    /**
     * 检查是否是近战攻击
     */
    private boolean isMeleeAttack(EntityDamageByEntityEvent event) {
        Entity damager = event.getDamager();

        // 如果伤害来源是玩家，则认为是近战攻击
        if (damager instanceof Player) {
            return true;
        }

        // 如果伤害来源是抛射物，则不是近战攻击
        if (damager instanceof org.bukkit.entity.Projectile) {
            return false;
        }

        // 其他情况（如TNT、岩浆等）也不认为是近战攻击
        return false;
    }
    
    /**
     * 处理投射物命中事件（巫妖诅咒效果和恐惧魔王影压）
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onProjectileHit(ProjectileHitEvent event) {
        // 处理箭矢命中（巫妖诅咒）
        if (event.getEntity() instanceof Arrow arrow && event.getHitEntity() instanceof Player hitPlayer) {
            handleArrowHit(arrow, hitPlayer);
            return;
        }

        // 处理凋灵之首命中（恐惧魔王影压）
        if (event.getEntity() instanceof org.bukkit.entity.WitherSkull witherSkull) {
            handleWitherSkullHit(witherSkull, event);
            return;
        }
    }

    /**
     * 处理箭矢命中（巫妖诅咒效果）
     */
    private void handleArrowHit(Arrow arrow, Player hitPlayer) {

        // 检查箭矢是否有巫妖诅咒标记
        if (!arrow.hasMetadata("lich_curse")) {
            return;
        }

        // 获取射箭者UUID
        String shooterUUIDStr = arrow.getMetadata("lich_curse").get(0).asString();
        try {
            java.util.UUID shooterUUID = java.util.UUID.fromString(shooterUUIDStr);
            Player shooter = org.bukkit.Bukkit.getPlayer(shooterUUID);

            if (shooter != null) {
                // 应用诅咒效果
                hitPlayer.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 60, 1)); // 减速二 3秒
                hitPlayer.addPotionEffect(new PotionEffect(PotionEffectType.WITHER, 100, 1)); // 凋零Ⅱ 5秒

                com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(hitPlayer,
                    com.projectSource.ultimateManhurt.util.ComponentUtil.warning("受到巫妖诅咒影响！"));
                com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(shooter,
                    com.projectSource.ultimateManhurt.util.ComponentUtil.info("巫妖诅咒命中目标！"));

                // 检查射箭者的战斗专注状态
                Profession shooterProfession = plugin.getProfessionManager().getPlayerProfession(shooterUUID);
                if (shooterProfession == Profession.HUNTER) {
                    ActiveSkillHandler activeHandler = plugin.getProfessionManager().getActiveSkillHandler();
                    if (activeHandler.isCombatFocusActive(shooterUUID)) {
                        // 射箭回复4.5点生命值
                        double currentHealth = shooter.getHealth();
                        double maxHealth = shooter.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
                        shooter.setHealth(Math.min(maxHealth, currentHealth + 4.5));

                        com.projectSource.ultimateManhurt.util.ComponentUtil.sendMessage(shooter,
                            com.projectSource.ultimateManhurt.util.ComponentUtil.info("战斗专注：射箭回复生命值！"));
                    }
                }
            }
        } catch (IllegalArgumentException e) {
            // UUID格式错误，忽略
        }
    }

    /**
     * 处理凋灵之首命中（恐惧魔王影压）
     */
    private void handleWitherSkullHit(org.bukkit.entity.WitherSkull witherSkull, ProjectileHitEvent event) {
        // 检查是否是恐惧魔王的影压技能
        if (!witherSkull.hasMetadata("fear_lord_shadow_strike")) {
            return;
        }

        // 检查是否已经处理过（防止重复处理）
        if (witherSkull.hasMetadata("fear_lord_processed")) {
            return;
        }

        // 标记为已处理
        witherSkull.setMetadata("fear_lord_processed", new org.bukkit.metadata.FixedMetadataValue(plugin, true));

        // 取消原生的凋灵之首伤害和效果
        event.setCancelled(true);

        // 获取自定义伤害值
        double damage = witherSkull.getMetadata("fear_lord_shadow_strike").get(0).asDouble();

        // 获取射击者UUID
        String shooterUUIDStr = witherSkull.getMetadata("fear_lord_shooter").get(0).asString();

        try {
            java.util.UUID shooterUUID = java.util.UUID.fromString(shooterUUIDStr);
            Player shooter = org.bukkit.Bukkit.getPlayer(shooterUUID);

            if (shooter == null) {
                return;
            }

            // 获取游戏会话
            GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(shooterUUID);
            if (gameSession == null) {
                return;
            }

            // 获取凋灵之首位置
            org.bukkit.Location skullLocation = witherSkull.getLocation();

            // 检查范围内的敌对玩家（1-2格范围）
            boolean hitTarget = false;
            int hitCount = 0;
            com.projectSource.ultimateManhurt.game.PlayerRole shooterRole = gameSession.getPlayerRole(shooter.getUniqueId());

            for (Player target : org.bukkit.Bukkit.getOnlinePlayers()) {
                com.projectSource.ultimateManhurt.game.PlayerRole targetRole = gameSession.getPlayerRole(target.getUniqueId());

                // 只攻击敌对阵营的玩家（不攻击队友和观察者）
                if (targetRole != null && targetRole != shooterRole && targetRole != com.projectSource.ultimateManhurt.game.PlayerRole.SPECTATOR) {
                    double distance = target.getLocation().distance(skullLocation);
                    if (distance <= 2.0) { // 1-2格范围内
                        // 造成真实伤害（直接减少血量，绕过所有防护）
                        double currentHealth = target.getHealth();
                        double newHealth = Math.max(0.5, currentHealth - damage); // 确保不会直接致死，至少保留0.5血
                        target.setHealth(newHealth);

                        // 播放受伤音效
                        target.playSound(target.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                        ComponentUtil.sendMessage(target, ComponentUtil.warning("受到恐惧魔王的影压攻击！受到 " + damage + " 点真实伤害"));
                        hitTarget = true;
                        hitCount++;
                    }
                }
            }

            // 更新恐惧魔王的连击状态
            ActiveSkillHandler activeHandler = plugin.getProfessionManager().getActiveSkillHandler();
            java.util.UUID shooterId = shooter.getUniqueId();

            if (hitTarget) {
                // 命中目标：增加连击次数（无论击中多少个目标，连击只+1）
                int currentCombo = activeHandler.getFearLordComboCount(shooterId);
                int newCombo = currentCombo + 1;
                activeHandler.setFearLordComboCount(shooterId, newCombo);
                ComponentUtil.sendMessage(shooter, ComponentUtil.info("影压命中" + hitCount + "个目标！连击次数: " + newCombo));

                plugin.getLogger().info("恐惧魔王 " + shooter.getName() + " 影压命中，连击从 " + currentCombo + " 增加到 " + newCombo);
            } else {
                // 未命中目标：重置连击次数
                activeHandler.setFearLordComboCount(shooterId, 0);
                ComponentUtil.sendMessage(shooter, ComponentUtil.warning("影压未命中，连击重置！"));

                plugin.getLogger().info("恐惧魔王 " + shooter.getName() + " 影压未命中，连击重置为0");
            }

            // 显示爆炸效果
            skullLocation.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION, skullLocation, 5, 1, 1, 1, 0.1);
            skullLocation.getWorld().playSound(skullLocation, org.bukkit.Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 0.8f);

        } catch (IllegalArgumentException e) {
            // UUID格式错误，忽略
        }
    }

    /**
     * 处理掉落伤害事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDamage(EntityDamageEvent event) {
        if (!(event.getEntity() instanceof Player player)) {
            return;
        }

        // 检查是否是掉落伤害
        if (event.getCause() != EntityDamageEvent.DamageCause.FALL) {
            return;
        }

        // 检查玩家是否处于蜘蛛跳跃状态
        ActiveSkillHandler activeHandler = plugin.getProfessionManager().getActiveSkillHandler();
        if (activeHandler.isSpiderLeaping(player.getUniqueId())) {
            // 免疫掉落伤害
            event.setCancelled(true);

            // 蜘蛛摔落造成自己最大生命值40%的伤害
            double maxHealth = player.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
            double currentHealth = player.getHealth();
            double selfDamage = maxHealth * 0.40;
            double newHealth = Math.max(1.0, currentHealth - selfDamage); // 确保不会死亡
            player.setHealth(newHealth);

            // 处理蜘蛛落地伤害
            activeHandler.handleSpiderLanding(player);

            plugin.getLogger().info("蜘蛛 " + player.getName() + " 跳跃落地，免疫掉落伤害，受到40%最大生命值伤害，并触发落地攻击");
            return;
        }

        // 检查莱娜是否处于钩爪摔落伤害免疫状态
        if (activeHandler.isLenaFallDamageImmune(player.getUniqueId())) {
            // 免疫掉落伤害
            event.setCancelled(true);
            ComponentUtil.sendMessage(player, ComponentUtil.info("钩爪保护！免疫摔落伤害"));

            // 落地后为周围友军提供抗性提升1效果15秒
            provideLenaLandingResistance(player);

            plugin.getLogger().info("莱娜 " + player.getName() + " 钩爪免疫摔落伤害并为友军提供抗性提升");
        }
    }

    /**
     * 处理实体死亡事件（萨满击杀奖励和狼死亡）
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onEntityDeath(EntityDeathEvent event) {
        // 处理玩家死亡（萨满击杀奖励）
        if (event.getEntity() instanceof Player deadPlayer) {
            handlePlayerDeath(deadPlayer);
        }

        // 处理狗死亡
        if (event.getEntity() instanceof org.bukkit.entity.Wolf dog) {
            handleDogDeath(dog);
        }
    }

    /**
     * 处理玩家死亡，检查萨满击杀奖励
     */
    private void handlePlayerDeath(Player deadPlayer) {
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(deadPlayer.getUniqueId());
        if (gameSession == null) {
            return;
        }

        // 检查房间是否启用职业系统
        if (!gameSession.getRoom().getSettings().isProfessionSystemEnabled()) {
            return;
        }

        com.projectSource.ultimateManhurt.game.PlayerRole deadPlayerRole = gameSession.getPlayerRole(deadPlayer.getUniqueId());

        // 寻找可能的击杀者
        Player killer = deadPlayer.getKiller();
        if (killer != null) {
            Profession killerProfession = plugin.getProfessionManager().getPlayerProfession(killer.getUniqueId());

            // 萨满击杀猎人，奖励狼
            if (killerProfession == Profession.SHAMAN && deadPlayerRole == com.projectSource.ultimateManhurt.game.PlayerRole.HUNTER) {
                plugin.getProfessionManager().getPassiveSkillHandler().handleShamanKillReward(killer);
            }

            // 恐惧魔王击杀速通者，获得18秒增益效果
            if (killerProfession == Profession.FEAR_LORD && deadPlayerRole == com.projectSource.ultimateManhurt.game.PlayerRole.SPEEDRUNNER) {
                plugin.getProfessionManager().getPassiveSkillHandler().handleFearLordSoulSiphonKill(killer, deadPlayer);
            }
        }
    }

    /**
     * 处理狗死亡
     */
    private void handleDogDeath(org.bukkit.entity.Wolf dog) {
        // 检查是否是萨满的狗
        if (dog.isTamed() && dog.getOwner() instanceof Player shaman) {
            Profession shamanProfession = plugin.getProfessionManager().getPlayerProfession(shaman.getUniqueId());
            if (shamanProfession == Profession.SHAMAN) {
                // 处理萨满狗死亡
                plugin.getProfessionManager().getPassiveSkillHandler().handleDogDeath(dog, shaman);
            }
        }
    }

    /**
     * 处理萨满狗战斗AI
     */
    private void handleShamanDogCombat(Player attacker, Player victim) {
        // 检查攻击者是否是萨满
        Profession attackerProfession = plugin.getProfessionManager().getPlayerProfession(attacker.getUniqueId());
        if (attackerProfession == Profession.SHAMAN) {
            // 萨满攻击敌人，让狗攻击目标
            plugin.getProfessionManager().getPassiveSkillHandler().commandShamanDogsToAttack(attacker, victim);
        }

        // 检查受害者是否是萨满
        Profession victimProfession = plugin.getProfessionManager().getPlayerProfession(victim.getUniqueId());
        if (victimProfession == Profession.SHAMAN) {
            // 萨满被攻击，让狗攻击攻击者
            plugin.getProfessionManager().getPassiveSkillHandler().commandShamanDogsToAttack(victim, attacker);
        }
    }

    /**
     * 处理猪灵光环伤害加深
     */
    private void handlePiglinAuraDamageBoost(EntityDamageByEntityEvent event, Player attacker, Player victim) {
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(attacker.getUniqueId());
        if (gameSession == null) {
            return;
        }

        // 检查受害者是否是速通者
        if (gameSession.getPlayerRole(victim.getUniqueId()) != com.projectSource.ultimateManhurt.game.PlayerRole.SPEEDRUNNER) {
            return;
        }

        // 寻找范围内的猪灵
        for (Player onlinePlayer : org.bukkit.Bukkit.getOnlinePlayers()) {
            // 检查是否是猪灵职业
            Profession profession = plugin.getProfessionManager().getPlayerProfession(onlinePlayer.getUniqueId());
            if (profession != Profession.PIGLIN) {
                continue;
            }

            // 检查是否是同一游戏会话的猎人
            if (gameSession.getPlayerRole(onlinePlayer.getUniqueId()) != com.projectSource.ultimateManhurt.game.PlayerRole.HUNTER) {
                continue;
            }

            // 检查是否在同一世界
            if (!victim.getWorld().equals(onlinePlayer.getWorld())) {
                continue; // 不在同一世界，跳过
            }

            // 检查距离
            double distance = victim.getLocation().distance(onlinePlayer.getLocation());
            if (distance <= 30.0) { // 30格范围内
                // 增加10%伤害
                double originalDamage = event.getDamage();
                double boostedDamage = originalDamage * 1.10;
                event.setDamage(boostedDamage);

                plugin.getLogger().info("猪灵 " + onlinePlayer.getName() + " 的光环为攻击者增加了10%伤害");
                return; // 找到一个猪灵就足够了
            }
        }
    }

    /**
     * 为莱娜落地后周围友军提供抗性提升效果
     */
    private void provideLenaLandingResistance(Player lena) {
        // 获取游戏会话
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(lena.getUniqueId());
        if (gameSession == null) {
            return;
        }

        // 获取莱娜的角色
        com.projectSource.ultimateManhurt.game.PlayerRole lenaRole = gameSession.getPlayerRole(lena.getUniqueId());
        if (lenaRole == null) {
            return;
        }

        // 为周围10格内的友军提供抗性提升1效果15秒
        int affectedCount = 0;
        for (Player nearbyPlayer : org.bukkit.Bukkit.getOnlinePlayers()) {
            // 检查是否是友军
            com.projectSource.ultimateManhurt.game.PlayerRole nearbyRole = gameSession.getPlayerRole(nearbyPlayer.getUniqueId());
            if (nearbyRole == null || nearbyRole != lenaRole) {
                continue;
            }

            // 检查距离（10格内）
            double distance = lena.getLocation().distance(nearbyPlayer.getLocation());
            if (distance <= 10.0) {
                // 给予抗性提升1效果15秒
                nearbyPlayer.addPotionEffect(new org.bukkit.potion.PotionEffect(
                    org.bukkit.potion.PotionEffectType.RESISTANCE, 300, 0)); // 15秒抗性1

                // 播放效果
                nearbyPlayer.spawnParticle(org.bukkit.Particle.TOTEM_OF_UNDYING,
                    nearbyPlayer.getLocation().add(0, 1, 0), 10, 0.5, 0.5, 0.5, 0.1);

                ComponentUtil.sendMessage(nearbyPlayer, ComponentUtil.info("受到莱娜钩爪落地的庇护！获得抗性提升15秒"));
                affectedCount++;
            }
        }

        // 播放莱娜的效果
        lena.playSound(lena.getLocation(), org.bukkit.Sound.BLOCK_BEACON_ACTIVATE, 1.0f, 1.2f);
        lena.spawnParticle(org.bukkit.Particle.ENCHANT, lena.getLocation().add(0, 1, 0), 30, 2, 2, 2, 0.1);

        ComponentUtil.sendMessage(lena, ComponentUtil.info("钩爪落地！为 " + affectedCount + " 名友军提供了抗性提升"));

        plugin.getLogger().info("莱娜 " + lena.getName() + " 钩爪落地为 " + affectedCount + " 名友军提供了抗性提升");
    }

    /**
     * 处理船长海灵诅咒伤害分担机制
     */
    private void handleCaptainSeaCurseDamageSharing(Player victim, EntityDamageByEntityEvent event) {
        // 获取受害者的游戏会话
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(victim.getUniqueId());
        if (gameSession == null) {
            return;
        }

        // 获取受害者角色
        com.projectSource.ultimateManhurt.game.PlayerRole victimRole = gameSession.getPlayerRole(victim.getUniqueId());
        if (victimRole == null) {
            return;
        }

        // 寻找周围60格内处于海灵诅咒状态的船长
        Player protectingCaptain = null;
        double minDistance = Double.MAX_VALUE;

        for (Player onlinePlayer : org.bukkit.Bukkit.getOnlinePlayers()) {
            // 检查是否是船长
            Profession profession = plugin.getProfessionManager().getPlayerProfession(onlinePlayer.getUniqueId());
            if (profession != Profession.CAPTAIN) {
                continue;
            }

            // 检查是否处于海灵诅咒状态
            if (!plugin.getProfessionManager().getActiveSkillHandler().isCaptainSeaCurseActive(onlinePlayer.getUniqueId())) {
                continue;
            }

            // 检查是否是友军
            com.projectSource.ultimateManhurt.game.PlayerRole captainRole = gameSession.getPlayerRole(onlinePlayer.getUniqueId());
            if (captainRole == null || captainRole != victimRole) {
                continue;
            }

            // 检查距离（60格内）
            double distance = victim.getLocation().distance(onlinePlayer.getLocation());
            if (distance <= 60.0 && distance < minDistance) {
                protectingCaptain = onlinePlayer;
                minDistance = distance;
            }
        }

        // 如果找到了保护的船长，执行伤害分担
        if (protectingCaptain != null) {
            double originalDamage = event.getDamage();

            // 受害者伤害减免75%
            double reducedDamage = originalDamage * 0.25; // 只承受25%伤害
            event.setDamage(reducedDamage);

            // 船长承担80%的原始伤害
            double captainDamage = originalDamage * 0.80;

            // 延迟执行船长伤害，避免事件冲突
            final Player finalCaptain = protectingCaptain;
            org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
                if (finalCaptain.isOnline()) {
                    // 确保船长不会因此死亡
                    double currentHealth = finalCaptain.getHealth();
                    double finalDamage = Math.min(captainDamage, currentHealth - 0.5); // 至少保留0.5血

                    if (finalDamage > 0) {
                        finalCaptain.damage(finalDamage);
                        ComponentUtil.sendMessage(finalCaptain, ComponentUtil.warning("海灵诅咒：承担了 " + String.format("%.1f", finalDamage) + " 点伤害"));
                        ComponentUtil.sendMessage(victim, ComponentUtil.info("受到船长 " + finalCaptain.getName() + " 的海灵诅咒保护！"));
                    }
                }
            }, 1L); // 延迟1 tick执行

            plugin.getLogger().info("船长 " + protectingCaptain.getName() + " 的海灵诅咒为 " + victim.getName() + " 分担了伤害");
        }
    }

    /**
     * 检查是否是剑
     */
    private boolean isSword(Material material) {
        return switch (material) {
            case WOODEN_SWORD, STONE_SWORD, IRON_SWORD, GOLDEN_SWORD, DIAMOND_SWORD, NETHERITE_SWORD -> true;
            default -> false;
        };
    }

    /**
     * 处理方块放置事件（机器人分则能成）
     */
    @EventHandler
    public void onBlockPlace(BlockPlaceEvent event) {
        Player player = event.getPlayer();
        Profession profession = plugin.getProfessionManager().getPlayerProfession(player.getUniqueId());

        if (profession == Profession.ROBOT) {
            // 每放置一个方块就计数
            passiveSkillHandler.handleRobotBlockPlacement(player);
        }
    }

    /**
     * 处理玩家移动事件（机器人地雷触发检测）
     */
    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();

        // 检查地雷触发
        plugin.getProfessionManager().getActiveSkillHandler().checkTrapTrigger(player, player.getLocation());
    }





    /**
     * 处理背包点击事件（女巫药水转换）
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player player)) return;

        Profession profession = plugin.getProfessionManager().getPlayerProfession(player.getUniqueId());
        if (profession != Profession.WITCH) return;

        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null) return;

        // 检查是否是右键点击水瓶
        if (event.getClick() == ClickType.RIGHT &&
            (clickedItem.getType() == Material.POTION || clickedItem.getType() == Material.SPLASH_POTION) &&
            !passiveSkillHandler.isMagicPotion(clickedItem) &&
            !passiveSkillHandler.isPoisonPotion(clickedItem)) {

            // 转换药水
            passiveSkillHandler.handleWitchPotionMastery(player, clickedItem);

            // 取消事件，防止物品移动
            event.setCancelled(true);

            // 更新背包显示
            player.updateInventory();
        }
    }

    /**
     * 处理生命值恢复事件（巫毒诅咒禁止回血）
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityRegainHealth(EntityRegainHealthEvent event) {
        if (!(event.getEntity() instanceof Player player)) return;

        // 检查是否受到巫毒诅咒影响
        if (plugin.getProfessionManager().getActiveSkillHandler().isVoodooCursed(player.getUniqueId())) {
            // 取消所有形式的生命恢复
            event.setCancelled(true);
        }
    }

    /**
     * 处理药水消耗事件（女巫魔药效果）
     */
    @EventHandler
    public void onPlayerItemConsume(PlayerItemConsumeEvent event) {
        Player player = event.getPlayer();
        Profession profession = plugin.getProfessionManager().getPlayerProfession(player.getUniqueId());

        if (profession == Profession.WITCH) {
            ItemStack item = event.getItem();

            // 检查是否是魔药
            if (passiveSkillHandler.isMagicPotion(item)) {
                // 使用魔药效果
                passiveSkillHandler.useMagicPotion(player);
            }
        }
    }

    /**
     * 处理喷溅药水事件（女巫毒药效果）
     */
    @EventHandler
    public void onPotionSplash(PotionSplashEvent event) {
        if (!(event.getPotion().getShooter() instanceof Player thrower)) return;

        Profession profession = plugin.getProfessionManager().getPlayerProfession(thrower.getUniqueId());
        if (profession != Profession.WITCH) return;

        // 检查是否是毒药
        ItemStack potionItem = event.getPotion().getItem();
        if (passiveSkillHandler.isPoisonPotion(potionItem)) {
            // 获取游戏会话
            GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(thrower.getUniqueId());
            if (gameSession == null) return;

            com.projectSource.ultimateManhurt.game.PlayerRole throwerRole = gameSession.getPlayerRole(thrower.getUniqueId());

            // 对受影响的玩家施加毒药效果
            for (org.bukkit.entity.LivingEntity affected : event.getAffectedEntities()) {
                if (affected instanceof Player target) {
                    com.projectSource.ultimateManhurt.game.PlayerRole targetRole = gameSession.getPlayerRole(target.getUniqueId());

                    // 只对敌对阵营生效
                    if (targetRole != null && targetRole != throwerRole && targetRole != com.projectSource.ultimateManhurt.game.PlayerRole.SPECTATOR) {
                        // 施加毒药效果
                        passiveSkillHandler.applyPoisonEffect(target);
                    }
                }
            }
        }
    }
}
