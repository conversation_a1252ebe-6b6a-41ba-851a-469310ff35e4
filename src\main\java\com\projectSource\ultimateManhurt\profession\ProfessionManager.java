package com.projectSource.ultimateManhurt.profession;

import com.projectSource.ultimateManhurt.UltimateManhurt;
import com.projectSource.ultimateManhurt.game.GameSession;
import com.projectSource.ultimateManhurt.game.PlayerRole;
import com.projectSource.ultimateManhurt.profession.skill.SkillCooldown;
import com.projectSource.ultimateManhurt.profession.util.PseudoRandom;
import com.projectSource.ultimateManhurt.util.ComponentUtil;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitTask;

import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 职业管理器
 * 负责职业选择、技能管理、效果应用等核心功能
 */
public class ProfessionManager {
    
    private final UltimateManhurt plugin;
    
    // 玩家职业映射 <玩家UUID, 职业>
    private final ConcurrentHashMap<UUID, Profession> playerProfessions;
    
    // 技能冷却管理器
    private final SkillCooldown skillCooldown;
    
    // 伪随机系统
    private final PseudoRandom pseudoRandom;

    // 主动技能处理器
    private final com.projectSource.ultimateManhurt.profession.skill.ActiveSkillHandler activeSkillHandler;

    // 被动技能处理器
    private final com.projectSource.ultimateManhurt.profession.skill.PassiveSkillHandler passiveSkillHandler;

    // 技能Boss Bar管理器
    private final com.projectSource.ultimateManhurt.profession.skill.SkillBossBarManager skillBossBarManager;

    // 定时任务
    private BukkitTask cleanupTask;
    private BukkitTask timedPassiveSkillTask;
    
    public ProfessionManager(UltimateManhurt plugin) {
        this.plugin = plugin;
        this.playerProfessions = new ConcurrentHashMap<>();
        this.skillCooldown = new SkillCooldown();
        this.pseudoRandom = new PseudoRandom();
        this.activeSkillHandler = new com.projectSource.ultimateManhurt.profession.skill.ActiveSkillHandler(plugin);
        this.passiveSkillHandler = new com.projectSource.ultimateManhurt.profession.skill.PassiveSkillHandler(plugin, pseudoRandom);
        this.skillBossBarManager = new com.projectSource.ultimateManhurt.profession.skill.SkillBossBarManager(plugin);

        initialize();
    }
    
    /**
     * 初始化职业管理器
     */
    private void initialize() {
        // 启动清理任务（每5分钟清理一次过期数据）
        cleanupTask = Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, () -> {
            skillCooldown.cleanupExpiredCooldowns();
        }, 6000L, 6000L); // 5分钟 = 6000 ticks
        
        // 启动定时被动技能任务（每5秒检查一次）
        timedPassiveSkillTask = Bukkit.getScheduler().runTaskTimer(plugin,
            this::processTimedPassiveSkills, 100L, 100L); // 每5秒 = 100 ticks
        
        plugin.getLogger().info("职业管理器已初始化");
    }
    
    /**
     * 为玩家设置职业
     */
    public boolean setProfession(UUID playerId, Profession profession) {
        Player player = Bukkit.getPlayer(playerId);
        if (player == null) {
            return false;
        }
        
        // 检查玩家角色是否匹配
        GameSession gameSession = plugin.getGameManager().getGameSessionByPlayer(playerId);
        if (gameSession == null) {
            return false;
        }
        
        PlayerRole playerRole = gameSession.getPlayerRole(playerId);
        if (playerRole == null || !profession.isAvailableForRole(playerRole)) {
            return false;
        }
        
        // 清理之前职业的特殊状态
        Profession oldProfession = playerProfessions.get(playerId);
        if (oldProfession == Profession.SHAMAN) {
            passiveSkillHandler.clearShamanDogs(playerId);
        } else if (oldProfession == Profession.LENA) {
            passiveSkillHandler.stopLenaForestBlessing(playerId);
        } else if (oldProfession == Profession.FEAR_LORD) {
            passiveSkillHandler.resetFearLordAttributes(player);
        }

        // 清除旧职业的状态
        clearPlayerData(playerId);

        // 设置新职业
        playerProfessions.put(playerId, profession);

        // 更新技能Boss Bar
        Player onlinePlayer = org.bukkit.Bukkit.getPlayer(playerId);
        if (onlinePlayer != null && onlinePlayer.isOnline()) {
            skillBossBarManager.onProfessionChange(onlinePlayer);
        }

        // 处理特殊职业的初始化
        if (profession == Profession.SHAMAN) {
            // 萨满获得初始狼伙伴
            passiveSkillHandler.handleShamanAnimalCompanion(player);
        } else if (profession == Profession.LENA) {
            // 莱娜启动森之祝福光环
            passiveSkillHandler.startLenaForestBlessing(player);
        }

        // 通知玩家
        ComponentUtil.sendMessage(player, ComponentUtil.success(
            "已选择职业: " + profession.getDisplayName()));

        plugin.getLogger().info("玩家 " + player.getName() + " 选择了职业: " + profession.getDisplayName());
        return true;
    }
    
    /**
     * 获取玩家的职业
     */
    public Profession getPlayerProfession(UUID playerId) {
        return playerProfessions.get(playerId);
    }
    
    /**
     * 检查玩家是否有职业
     */
    public boolean hasPlayerProfession(UUID playerId) {
        return playerProfessions.containsKey(playerId);
    }
    
    /**
     * 移除玩家的职业
     */
    public void removePlayerProfession(UUID playerId) {
        // 清理特殊职业的状态
        Profession oldProfession = playerProfessions.get(playerId);
        if (oldProfession == Profession.SHAMAN) {
            passiveSkillHandler.clearShamanDogs(playerId);
        } else if (oldProfession == Profession.LENA) {
            passiveSkillHandler.stopLenaForestBlessing(playerId);
        } else if (oldProfession == Profession.FEAR_LORD) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null) {
                passiveSkillHandler.resetFearLordAttributes(player);
            }
        }

        playerProfessions.remove(playerId);
        clearPlayerData(playerId);
    }
    
    /**
     * 使用主动技能
     */
    public boolean useActiveSkill(UUID playerId) {
        Player player = Bukkit.getPlayer(playerId);
        if (player == null) {
            return false;
        }
        
        Profession profession = getPlayerProfession(playerId);
        if (profession == null) {
            return false;
        }
        
        String skillName = profession.getActiveSkillName();
        
        // 检查冷却时间
        if (skillCooldown.isOnCooldown(playerId, skillName)) {
            int remaining = skillCooldown.getRemainingCooldown(playerId, skillName);
            ComponentUtil.sendMessage(player, ComponentUtil.error(
                "技能 " + skillName + " 冷却中，剩余 " + remaining + " 秒"));
            return false;
        }
        
        // 执行技能效果
        boolean success = executeActiveSkill(player, profession);
        
        if (success && profession.getActiveSkillCooldown() > 0) {
            // 设置冷却时间
            skillCooldown.setCooldown(playerId, skillName, profession.getActiveSkillCooldown());

            // 触发Boss Bar更新
            skillBossBarManager.createSkillBossBar(player);
        }
        
        return success;
    }
    
    /**
     * 执行主动技能效果
     */
    private boolean executeActiveSkill(Player player, Profession profession) {
        return activeSkillHandler.executeActiveSkill(player, profession);
    }

    /**
     * 处理定时被动技能
     * 只处理需要定时触发的被动技能（如猎人自然祝福）
     */
    private void processTimedPassiveSkills() {
        for (UUID playerId : playerProfessions.keySet()) {
            Player player = Bukkit.getPlayer(playerId);
            if (player == null || !player.isOnline()) {
                continue;
            }

            Profession profession = playerProfessions.get(playerId);
            if (profession == null) {
                continue;
            }

            // 处理需要定时触发的被动技能
            switch (profession) {
                case HUNTER:
                    // 猎人自然祝福 - 每45秒触发一次
                    passiveSkillHandler.handleHunterNatureBlessing(player);
                    break;
                default:
                    // 其他被动技能在事件监听器中处理
                    break;
            }
        }
    }
    

    

    
    /**
     * 获取技能冷却管理器
     */
    public SkillCooldown getSkillCooldown() {
        return skillCooldown;
    }

    /**
     * 获取技能Boss Bar管理器
     */
    public com.projectSource.ultimateManhurt.profession.skill.SkillBossBarManager getSkillBossBarManager() {
        return skillBossBarManager;
    }
    
    /**
     * 获取伪随机系统
     */
    public PseudoRandom getPseudoRandom() {
        return pseudoRandom;
    }

    /**
     * 获取主动技能处理器
     */
    public com.projectSource.ultimateManhurt.profession.skill.ActiveSkillHandler getActiveSkillHandler() {
        return activeSkillHandler;
    }

    /**
     * 获取被动技能处理器
     */
    public com.projectSource.ultimateManhurt.profession.skill.PassiveSkillHandler getPassiveSkillHandler() {
        return passiveSkillHandler;
    }
    
    /**
     * 清除玩家数据
     */
    private void clearPlayerData(UUID playerId) {
        skillCooldown.clearPlayerCooldowns(playerId);
        pseudoRandom.clearPlayerStates(playerId);
        activeSkillHandler.clearPlayerStates(playerId);

        // 清理技能Boss Bar
        Player player = org.bukkit.Bukkit.getPlayer(playerId);
        if (player != null && player.isOnline()) {
            skillBossBarManager.removeSkillBossBar(player);
        }
    }
    
    /**
     * 关闭职业管理器
     */
    public void shutdown() {
        if (cleanupTask != null) {
            cleanupTask.cancel();
        }
        if (timedPassiveSkillTask != null) {
            timedPassiveSkillTask.cancel();
        }

        // 关闭被动技能处理器（包括猪灵光环任务）
        if (passiveSkillHandler != null) {
            passiveSkillHandler.shutdown();
        }

        // 关闭技能Boss Bar管理器
        if (skillBossBarManager != null) {
            skillBossBarManager.shutdown();
        }

        playerProfessions.clear();
        pseudoRandom.cleanup();

        plugin.getLogger().info("职业管理器已关闭");
    }
}
